#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Token控制测试脚本
验证新的token控制机制是否有效
"""

import sys
import os
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

def test_token_control():
    """测试token控制配置"""
    print("🧪 Token控制配置测试")
    print("=" * 50)
    
    try:
        from app import Config
        
        print("📋 当前配置:")
        print(f"  TOP_K: {Config.TOP_K}")
        print(f"  RERANK_TOP_K: {Config.RERANK_TOP_K}")
        print(f"  MAX_CONTEXT_LENGTH: {Config.MAX_CONTEXT_LENGTH:,}")
        print(f"  MAX_NODE_LENGTH: {Config.MAX_NODE_LENGTH:,}")
        print(f"  RELEVANCE_THRESHOLD: {Config.RELEVANCE_THRESHOLD}")
        
        # 估算token使用
        estimated_tokens = estimate_token_usage()
        print(f"\n📊 预估token使用:")
        print(f"  最大上下文: ~{estimated_tokens:,} tokens")
        print(f"  安全限制: 65,536 tokens")
        
        if estimated_tokens < 50000:
            print("✅ Token使用在安全范围内")
            return True
        else:
            print("⚠️ Token使用可能仍然过高")
            return False
            
    except Exception as e:
        print(f"❌ 配置测试失败: {str(e)}")
        return False

def estimate_token_usage():
    """估算token使用量"""
    from app import Config
    
    # 估算公式（粗略）：
    # 中文：1个字符 ≈ 1.5 tokens
    # 英文：1个字符 ≈ 0.25 tokens
    # 混合文本：1个字符 ≈ 1 token
    
    max_context_chars = Config.MAX_CONTEXT_LENGTH
    estimated_tokens = max_context_chars * 1.2  # 保守估计
    
    return int(estimated_tokens)

def test_content_limiter():
    """测试内容长度限制器"""
    print("\n🔧 内容长度限制器测试")
    print("=" * 50)
    
    try:
        # 模拟节点数据
        class MockNode:
            def __init__(self, text, score=0.8):
                self.text = text
                self.score = score
        
        # 创建测试节点
        test_nodes = [
            MockNode("A" * 20000, 0.9),  # 超长节点
            MockNode("B" * 10000, 0.8),  # 正常节点
            MockNode("C" * 15000, 0.7),  # 中等节点
            MockNode("D" * 5000, 0.6),   # 短节点
        ]
        
        print(f"原始节点数: {len(test_nodes)}")
        total_original = sum(len(node.text) for node in test_nodes)
        print(f"原始总长度: {total_original:,} 字符")
        
        # 导入并测试内容限制器
        from app import Config
        
        # 手动创建限制器类（因为它在函数内部定义）
        class ContentLengthLimiter:
            def __init__(self, max_total_length=30000, max_node_length=15000):
                self.max_total_length = max_total_length
                self.max_node_length = max_node_length
                
            def postprocess_nodes(self, nodes, query_bundle=None):
                limited_nodes = []
                total_length = 0
                
                for node in nodes:
                    # 截断过长的节点
                    if len(node.text) > self.max_node_length:
                        node.text = node.text[:self.max_node_length] + "...[内容已截断]"
                    
                    # 检查总长度
                    if total_length + len(node.text) <= self.max_total_length:
                        limited_nodes.append(node)
                        total_length += len(node.text)
                    else:
                        break
                
                return limited_nodes
        
        # 测试限制器
        limiter = ContentLengthLimiter(
            max_total_length=Config.MAX_CONTEXT_LENGTH,
            max_node_length=Config.MAX_NODE_LENGTH
        )
        
        limited_nodes = limiter.postprocess_nodes(test_nodes)
        
        print(f"限制后节点数: {len(limited_nodes)}")
        total_limited = sum(len(node.text) for node in limited_nodes)
        print(f"限制后总长度: {total_limited:,} 字符")
        print(f"压缩比例: {(1 - total_limited/total_original)*100:.1f}%")
        
        if total_limited <= Config.MAX_CONTEXT_LENGTH:
            print("✅ 内容长度限制器工作正常")
            return True
        else:
            print("❌ 内容长度限制器未能有效控制长度")
            return False
            
    except Exception as e:
        print(f"❌ 内容限制器测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 Token控制机制全面测试")
    print("=" * 60)
    
    results = []
    
    # 1. 配置测试
    results.append(("配置检查", test_token_control()))
    
    # 2. 内容限制器测试
    results.append(("内容限制器", test_content_limiter()))
    
    # 总结
    print("\n📊 测试结果总结")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 项测试通过")
    
    if passed == len(results):
        print("\n🎉 Token控制机制配置正确！")
        print("\n💡 优化效果:")
        print("  - TOP_K: 6 → 3 (减少50%)")
        print("  - RERANK_TOP_K: 2 → 1 (减少50%)")
        print("  - 添加内容长度限制器")
        print("  - 最大上下文: 30,000字符")
        print("  - 单节点限制: 15,000字符")
        print("\n🚀 现在可以安全使用应用了！")
    else:
        print("\n⚠️ 部分测试失败，请检查配置。")

if __name__ == "__main__":
    main()
