#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化的DeepDoc Excel处理方案
"""

import sys
from pathlib import Path
from app import load_excels_with_deepdoc, load_excels_traditional, create_nodes_from_excel
from llama_index.core.node_parser import Senten<PERSON><PERSON>plitter

def test_optimized_deepdoc():
    """测试优化的DeepDoc方案"""
    print("🧪 测试优化的DeepDoc Excel处理方案")
    
    # 设置数据目录
    data_dir = "./data"
    
    if not Path(data_dir).exists():
        print(f"❌ 数据目录不存在: {data_dir}")
        return False
    
    # 检查是否有Excel文件
    excel_files = list(Path(data_dir).rglob("*.xlsx"))
    if not excel_files:
        print(f"❌ 在 {data_dir} 中没有找到Excel文件")
        return False
    
    print(f"📊 找到 {len(excel_files)} 个Excel文件:")
    for file in excel_files:
        print(f"  - {file.name}")
    
    try:
        # 测试优化的DeepDoc数据加载
        print("\n🔄 测试优化的DeepDoc数据加载...")
        deepdoc_data = load_excels_with_deepdoc(data_dir)
        
        print(f"✅ 成功加载 {len(deepdoc_data)} 个数据条目")
        
        # 分析数据类型分布
        type_counts = {}
        processing_methods = {}
        
        for entry in deepdoc_data:
            metadata = entry.get("metadata", {})
            content_type = metadata.get("content_type", "unknown")
            processing_method = metadata.get("processing_method", "unknown")
            node_type = metadata.get("node_type", "data")
            
            key = f"{processing_method}_{node_type}"
            type_counts[key] = type_counts.get(key, 0) + 1
            processing_methods[processing_method] = processing_methods.get(processing_method, 0) + 1
        
        print(f"\n📈 处理方法分布:")
        for method, count in processing_methods.items():
            print(f"  - {method}: {count} 条")
        
        print(f"\n🏷️ 节点类型分布:")
        for data_type, count in type_counts.items():
            print(f"  - {data_type}: {count} 条")
        
        # 分析内容长度
        content_lengths = [len(entry["content"]) for entry in deepdoc_data]
        if content_lengths:
            avg_length = sum(content_lengths) / len(content_lengths)
            min_length = min(content_lengths)
            max_length = max(content_lengths)
            
            print(f"\n📏 内容长度统计:")
            print(f"  - 平均长度: {avg_length:.0f} 字符")
            print(f"  - 最小长度: {min_length} 字符")
            print(f"  - 最大长度: {max_length:,} 字符")
        
        # 显示不同类型的样本内容
        print(f"\n📝 样本内容:")
        
        # 显示概览节点
        overview_nodes = [entry for entry in deepdoc_data if entry.get("metadata", {}).get("node_type") == "overview"]
        if overview_nodes:
            print(f"  📊 概览节点样本:")
            content_preview = overview_nodes[0]["content"][:300] + "..." if len(overview_nodes[0]["content"]) > 300 else overview_nodes[0]["content"]
            print(f"    {content_preview}")
            print()
        
        # 显示数据块节点
        chunk_nodes = [entry for entry in deepdoc_data if entry.get("metadata", {}).get("processing_method") == "deepdoc_style_chunked"]
        if chunk_nodes:
            print(f"  📚 数据块节点样本:")
            content_preview = chunk_nodes[0]["content"][:300] + "..." if len(chunk_nodes[0]["content"]) > 300 else chunk_nodes[0]["content"]
            print(f"    {content_preview}")
            print()
        
        # 测试节点创建
        print("🔄 测试节点创建...")
        node_parser = SentenceSplitter(chunk_size=1024, chunk_overlap=200)
        nodes = create_nodes_from_excel(deepdoc_data, node_parser)
        
        print(f"✅ 成功创建 {len(nodes)} 个节点")
        
        # 分析节点
        node_lengths = [len(node.text) for node in nodes]
        if node_lengths:
            avg_node_length = sum(node_lengths) / len(node_lengths)
            min_node_length = min(node_lengths)
            max_node_length = max(node_lengths)
            
            print(f"\n📊 节点统计:")
            print(f"  - 平均节点长度: {avg_node_length:.0f} 字符")
            print(f"  - 最小节点长度: {min_node_length} 字符")
            print(f"  - 最大节点长度: {max_node_length:,} 字符")
        
        # 分析节点类型
        node_types = {}
        for node in nodes:
            node_type = node.metadata.get("node_type", "unknown")
            processing_method = node.metadata.get("processing_method", "unknown")
            key = f"{processing_method}_{node_type}"
            node_types[key] = node_types.get(key, 0) + 1
        
        print(f"\n🏷️ 最终节点类型分布:")
        for node_type, count in node_types.items():
            print(f"  - {node_type}: {count} 个节点")
        
        print("✅ 优化的DeepDoc处理方案测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def compare_deepdoc_vs_traditional():
    """比较优化DeepDoc方案与传统方案"""
    print("\n🔄 比较优化DeepDoc方案与传统方案...")
    
    data_dir = "./data"
    
    try:
        # 加载传统方案数据
        print("加载传统方案数据...")
        traditional_data = load_excels_traditional(data_dir)
        
        # 加载优化DeepDoc方案数据
        print("加载优化DeepDoc方案数据...")
        deepdoc_data = load_excels_with_deepdoc(data_dir)
        
        print(f"\n📊 数据量对比:")
        print(f"  - 传统方案: {len(traditional_data)} 条数据")
        print(f"  - 优化DeepDoc方案: {len(deepdoc_data)} 条数据")
        
        # 内容长度对比
        traditional_lengths = [len(entry["content"]) for entry in traditional_data]
        deepdoc_lengths = [len(entry["content"]) for entry in deepdoc_data]
        
        if traditional_lengths and deepdoc_lengths:
            print(f"\n📏 平均内容长度对比:")
            print(f"  - 传统方案: {sum(traditional_lengths)/len(traditional_lengths):.0f} 字符")
            print(f"  - 优化DeepDoc方案: {sum(deepdoc_lengths)/len(deepdoc_lengths):.0f} 字符")
        
        # 分析数据结构差异
        print(f"\n🔍 数据结构对比:")
        
        # 传统方案分析
        traditional_types = {}
        for entry in traditional_data:
            content_type = entry.get("metadata", {}).get("content_type", "unknown")
            traditional_types[content_type] = traditional_types.get(content_type, 0) + 1
        
        print(f"  传统方案类型:")
        for t_type, count in traditional_types.items():
            print(f"    - {t_type}: {count}")
        
        # DeepDoc方案分析
        deepdoc_types = {}
        for entry in deepdoc_data:
            processing_method = entry.get("metadata", {}).get("processing_method", "unknown")
            deepdoc_types[processing_method] = deepdoc_types.get(processing_method, 0) + 1
        
        print(f"  优化DeepDoc方案类型:")
        for d_type, count in deepdoc_types.items():
            print(f"    - {d_type}: {count}")
        
        print("✅ 方案对比完成")
        
    except Exception as e:
        print(f"❌ 对比失败: {str(e)}")

def analyze_qa_effectiveness():
    """分析问答效果"""
    print("\n🎯 分析问答效果...")
    
    data_dir = "./data"
    
    try:
        deepdoc_data = load_excels_with_deepdoc(data_dir)
        
        # 分析概览节点的问答价值
        overview_nodes = [entry for entry in deepdoc_data if entry.get("metadata", {}).get("node_type") == "overview"]
        chunk_nodes = [entry for entry in deepdoc_data if entry.get("metadata", {}).get("processing_method") == "deepdoc_style_chunked"]
        
        print(f"📊 问答节点分析:")
        print(f"  - 概览节点: {len(overview_nodes)} 个 (提供表格结构和概要信息)")
        print(f"  - 数据块节点: {len(chunk_nodes)} 个 (提供详细数据内容)")
        
        if overview_nodes:
            avg_overview_length = sum(len(node["content"]) for node in overview_nodes) / len(overview_nodes)
            print(f"  - 概览节点平均长度: {avg_overview_length:.0f} 字符")
        
        if chunk_nodes:
            avg_chunk_length = sum(len(node["content"]) for node in chunk_nodes) / len(chunk_nodes)
            print(f"  - 数据块节点平均长度: {avg_chunk_length:.0f} 字符")
        
        print(f"\n💡 优化效果:")
        print(f"  ✅ 增加了表格概览，便于理解数据结构")
        print(f"  ✅ 优化了数据块大小，提高检索精度")
        print(f"  ✅ 改进了内容格式，增强语义表示")
        print(f"  ✅ 保持了数据完整性和上下文关系")
        
    except Exception as e:
        print(f"❌ 分析失败: {str(e)}")

def main():
    """主函数"""
    print("🚀 开始测试优化的DeepDoc Excel处理方案\n")
    
    # 测试优化DeepDoc方案
    success = test_optimized_deepdoc()
    
    if success:
        # 比较方案
        compare_deepdoc_vs_traditional()
        
        # 分析问答效果
        analyze_qa_effectiveness()
    
    print("\n🏁 测试完成")

if __name__ == "__main__":
    main()
