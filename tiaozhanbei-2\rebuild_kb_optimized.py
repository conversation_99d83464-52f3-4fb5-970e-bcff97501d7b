#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
重新构建知识库 - 针对大型Excel文件优化版本
"""

import os
import sys
import shutil
from pathlib import Path

def main():
    """重新构建知识库"""
    print("🔄 开始重新构建知识库（优化版本）...")
    
    # 删除旧的向量数据库
    chroma_db_path = Path("chroma_db")
    if chroma_db_path.exists():
        print("🗑️ 删除旧的向量数据库...")
        shutil.rmtree(chroma_db_path)
        print("✅ 旧数据库已删除")
    
    # 删除旧的存储文件
    storage_path = Path("storage")
    if storage_path.exists():
        print("🗑️ 删除旧的存储文件...")
        shutil.rmtree(storage_path)
        print("✅ 旧存储文件已删除")
    
    # 运行构建脚本
    print("🏗️ 开始构建新的知识库...")
    try:
        import subprocess
        result = subprocess.run([sys.executable, "build_knowledge_base.py"], 
                              capture_output=True, text=True, cwd=".")
        
        if result.returncode == 0:
            print("✅ 知识库构建成功！")
            print("\n📊 构建输出:")
            print(result.stdout)
        else:
            print("❌ 知识库构建失败！")
            print("\n错误信息:")
            print(result.stderr)
            
    except Exception as e:
        print(f"❌ 构建过程出错: {str(e)}")
    
    print("\n🎯 优化说明:")
    print("- 减少了分块大小以控制token数量")
    print("- 限制了Excel文件的处理行数")
    print("- 减少了检索和重排序的文档数量")
    print("- 添加了内容大小限制")

if __name__ == "__main__":
    main()
