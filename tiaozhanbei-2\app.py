import torch
import json
import time
from pathlib import Path
from typing import List, Dict
import re
import chromadb
import streamlit as st
from llama_index.core import VectorStoreIndex, StorageContext, Settings, get_response_synthesizer
from llama_index.core.chat_engine import ContextChatEngine
from llama_index.core.memory import ChatMemoryBuffer
from llama_index.core.query_engine import RetrieverQueryEngine
from llama_index.core.schema import TextNode, Document, NodeWithScore
from llama_index.llms.huggingface import HuggingFaceLLM
from llama_index.embeddings.huggingface import HuggingFaceEmbedding
from llama_index.vector_stores.chroma import ChromaVectorStore
from llama_index.core import PromptTemplate
from llama_index.core.postprocessor import SentenceTransformerRerank
from llama_index.llms.openai_like import OpenAILike
from llama_index.core import get_response_synthesizer
from llama_index.core.retrievers import VectorIndexRetriever
from llama_index.retrievers.bm25 import BM25Retriever
from llama_index.core.retrievers import QueryFusionRetriever
import base64
import io
import nest_asyncio
from pypdf import PdfReader

# 应用nest_asyncio补丁，以在Streamlit等现有事件循环中运行异步LlamaIndex代码
nest_asyncio.apply()

# ================== Streamlit页面配置 ==================
st.set_page_config(
    page_title="南水北调水利问答助手",
    page_icon="assets/Picture/lixiahe.png",
    layout="centered",
    initial_sidebar_state="auto"
)


# 加载自定义CSS文件的函数
def load_css(file_path):
    try:
        with open(file_path, encoding='utf-8') as f:
            st.markdown(f"<style>{f.read()}</style>", unsafe_allow_html=True)
    except FileNotFoundError:
        st.error(f"CSS文件未找到: {file_path}")


def disable_streamlit_watcher():
    """Patch Streamlit to disable file watcher"""

    def _on_script_changed(_):
        return

    from streamlit import runtime
    runtime.get_instance()._on_script_changed = _on_script_changed


# 新增：生成文本文件的函数
def generate_single_qa_text(question, answer):
    """生成单次问答的文本文件，完全支持中文"""
    content = "南水北调水利问答\n\n"
    content += f"问题:\n{question}\n\n"
    content += f"回答:\n{answer}\n\n"
    content += f"生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}"

    return content.encode('utf-8')


# ================================ 配置类 ================================
class Config:
    EMBED_MODEL_PATH = r"D:\pythonProject\embedding_model\BAAI\bge-large-zh-v1___5"
    RERANK_MODEL_PATH = r"D:\pythonProject\llms\BAAI\bge-reranker-base"  # 新增重排序模型路径

    DATA_DIR = "./data"
    VECTOR_DB_DIR = "./chroma_db"
    PERSIST_DIR = "./storage"

    COLLECTION_NAME = "chinese_labor_laws"
    TOP_K = 15   # 大幅增加初筛范围，确保覆盖不同时间段的数据
    RERANK_TOP_K = 8  # 保留更多相关文档，提供充足上下文

    # 相关度过滤配置
    RELEVANCE_THRESHOLD = 0.05  # 进一步降低相关度阈值，避免过度过滤时间序列数据

    # Token控制配置
    MAX_CONTEXT_LENGTH = 30000  # 最大上下文长度（字符数）
    MAX_NODE_LENGTH = 15000     # 单个节点最大长度（字符数）

    # 文本分块配置
    # 可选值: 'semantic_double_merge' (语义双重合并分块), 'semantic' (语义分块), 'sentence' (传统句子分块)
    CHUNKING_MODE = 'semantic'
    # 传统分块参数 - 针对大型Excel文件优化
    CHUNK_SIZE = 1024  # 增加块大小以处理大型Excel
    CHUNK_OVERLAP = 200  # 减少重叠以节省token
    # 语义分块参数
    SEMANTIC_BREAKPOINT_THRESHOLD = 95  # 提高阈值，生成更大的节点
    # 语义双重合并分块参数
    SEMANTIC_DOUBLE_INITIAL_THRESHOLD = 0.4  # 初始分块阈值
    SEMANTIC_DOUBLE_APPENDING_THRESHOLD = 0.5  # 附加阈值
    SEMANTIC_DOUBLE_MERGING_THRESHOLD = 0.5  # 合并阈值
    SEMANTIC_DOUBLE_MAX_CHUNK_SIZE = 2048  # 减少最大块大小以控制token数量


# ================== 缓存资源初始化 ==================
@st.cache_resource(show_spinner="初始化模型中...")
def init_models():
    # 文本嵌入模型
    embed_model = HuggingFaceEmbedding(
        model_name=Config.EMBED_MODEL_PATH,
        device='cuda' if torch.cuda.is_available() else 'cpu'  # 指定设备
    )

    # 使用DeepSeek的OpenAI兼容API
    llm = OpenAILike(
        model="deepseek-chat",  # 可选模型：glm-4, glm-3-turbo, characterglm等
        api_base="https://api.deepseek.com",  # 关键！必须指定此端点
        api_key="***********************************",
        context_window=128000,  # 按需调整（glm-4实际支持128K）
        is_chat_model=True,
        is_function_calling_model=False,  # GLM暂不支持函数调用
        max_tokens=4096,  # 最大生成token数（按需调整）
        temperature=0.3,  # 推荐范围 0.1~1.0
        top_p=0.7  # 推荐范围 0.5~1.0
    )
        # 使用阿里云的OpenAI兼容API（提供统一的接口调用llm）
    # llm = OpenAILike(
    #         model="qwen-plus",  # 阿里云千问模型
    #         api_base="https://dashscope.aliyuncs.com/compatible-mode/v1",  # 阿里云API端点
    #         api_key="sk-1e2ce235b22d40afb019864b3f29b803",  # 请确保此密钥有效
    #         context_window=128000,    # 上下文窗口大小
    #         is_chat_model=True,
    #         is_function_calling_model=False,
    #         max_tokens=1024,          # 最大生成token数
    #         temperature=0.3,          # 温度参数
    #         top_p=0.7,                # top_p参数
    #         timeout=60,               # 增加超时时间到60秒
    #         max_retries=5             # 增加重试次数到5次
    #     )


    # 重排序模型
    reranker = SentenceTransformerRerank(
        model=Config.RERANK_MODEL_PATH,
        top_n=Config.RERANK_TOP_K
    )

    Settings.embed_model = embed_model
    Settings.llm = llm

    return embed_model, llm, reranker


# 新增：缓存NodeParser
@st.cache_resource(show_spinner="初始化文本分割器...")
def init_node_parser():
    """
    初始化语义感知文本分割器，根据内容语义自动调整块大小，避免语义割裂
    支持三种分块方式:
    1. 语义双重合并分块 (最先进，需要spaCy)
    2. 语义分块 (较先进)
    3. 传统句子分块 (回退方案)
    """
    # 从配置中获取分块模式
    chunking_mode = Config.CHUNKING_MODE
    
    try:
        if chunking_mode == 'semantic_double_merge':
            # 尝试导入语义双重合并分块所需的模块
            try:
                import spacy
                from llama_index.core.node_parser import (
                    SemanticDoubleMergingSplitterNodeParser,
                    LanguageConfig
                )
                
                # 检查是否已安装spaCy模型
                try:
                    nlp = spacy.load("zh_core_web_md")
                    print("成功加载中文spaCy模型")
                except OSError:
                    print("未找到中文spaCy模型，尝试使用英文模型")
                    try:
                        nlp = spacy.load("en_core_web_md")
                        print("成功加载英文spaCy模型")
                    except OSError:
                        raise ImportError("未找到所需的spaCy模型，请安装: python -m spacy download zh_core_web_md")
                
                # 配置语言设置
                language = "chinese" if "zh" in nlp.meta["lang"] else "english"
                config = LanguageConfig(
                    language=language,
                    spacy_model=nlp.meta["name"]
                )
                
                # 创建语义双重合并分块器，使用配置参数
                semantic_double_merger = SemanticDoubleMergingSplitterNodeParser(
                    language_config=config,
                    initial_threshold=Config.SEMANTIC_DOUBLE_INITIAL_THRESHOLD,
                    appending_threshold=Config.SEMANTIC_DOUBLE_APPENDING_THRESHOLD,
                    merging_threshold=Config.SEMANTIC_DOUBLE_MERGING_THRESHOLD,
                    max_chunk_size=Config.SEMANTIC_DOUBLE_MAX_CHUNK_SIZE
                )
                
                print(f"成功初始化语义双重合并分块器 (语言: {language})")
                return semantic_double_merger
                
            except (ImportError, Exception) as e:
                print(f"语义双重合并分块器初始化失败: {str(e)}，回退到语义分块器")
                chunking_mode = 'semantic'
        
        if chunking_mode == 'semantic':
            # 尝试导入语义分块所需的模块
            from llama_index.core.node_parser import SemanticSplitterNodeParser
            from llama_index.embeddings.huggingface import HuggingFaceEmbedding
            
            # 使用与系统相同的嵌入模型，保持一致性
            embed_model = HuggingFaceEmbedding(
                model_name=Config.EMBED_MODEL_PATH,
                device='cuda' if torch.cuda.is_available() else 'cpu'
            )
            
            # 创建语义分块器，使用配置参数
            semantic_splitter = SemanticSplitterNodeParser(
                buffer_size=1,  # 每次分析一个句子
                breakpoint_percentile_threshold=Config.SEMANTIC_BREAKPOINT_THRESHOLD,
                embed_model=embed_model
            )
            
            print("成功初始化语义分块器")
            return semantic_splitter
            
    except (ImportError, Exception) as e:
        print(f"语义分块器初始化失败: {str(e)}，回退到传统分块器")
    
    # 如果语义分块器初始化失败，回退到传统的SentenceSplitter
    from llama_index.core.node_parser import SentenceSplitter
    print("使用传统句子分块器")
    return SentenceSplitter(
        chunk_size=Config.CHUNK_SIZE,
        chunk_overlap=Config.CHUNK_OVERLAP
    )


@st.cache_resource(show_spinner="加载知识库中...")
def load_index():
    """
    根据官方文档推荐的最佳实践，从磁盘显式加载知识库的各个组件。
    增加详细的诊断信息来帮助调试向量数据库问题。
    """
    from llama_index.core.storage.docstore import SimpleDocumentStore
    persist_dir = Path(Config.PERSIST_DIR)
    db_dir = Path(Config.VECTOR_DB_DIR)
    docstore_path = persist_dir / "docstore.json"

    print(f"检查知识库路径:")
    print(f"  持久化目录: {persist_dir} (存在: {persist_dir.exists()})")
    print(f"  向量数据库目录: {db_dir} (存在: {db_dir.exists()})")
    print(f"  文档存储文件: {docstore_path} (存在: {docstore_path.exists()})")

    if not all([persist_dir.exists(), db_dir.exists(), docstore_path.exists()]):
        print("❌ 知识库目录或必要文件(docstore.json)不存在。")
        return None

    try:
        print("--- 正在加载知识库 ---")
        # 1. 显式加载向量数据库
        db = chromadb.PersistentClient(path=str(db_dir))
        chroma_collection = db.get_or_create_collection(Config.COLLECTION_NAME)

        # 检查向量数据库中的数据
        collection_count = chroma_collection.count()
        print(f"ChromaDB集合 '{Config.COLLECTION_NAME}' 中有 {collection_count} 个向量")

        if collection_count == 0:
            print("⚠️ 警告：向量数据库为空！这可能是导致查询失败的原因。")
            print("建议重新运行 'python build_knowledge_base.py' 来重建知识库。")

        vector_store = ChromaVectorStore(chroma_collection=chroma_collection)

        # 2. 显式加载文档库
        docstore = SimpleDocumentStore.from_persist_path(str(docstore_path))
        print(f"文档库加载成功，共 {len(docstore.docs)} 个文档。")

        if len(docstore.docs) == 0:
            print("⚠️ 警告：文档库为空！")

        # 3. 从加载的组件重建存储上下文和索引
        storage_context = StorageContext.from_defaults(
            docstore=docstore, vector_store=vector_store
        )
        # 修复：使用官方推荐的构造函数，而不是废弃的 from_storage
        index = VectorStoreIndex(nodes=[], storage_context=storage_context)

        # 验证索引是否可以正常工作
        try:
            # 尝试一个简单的检索测试
            retriever = index.as_retriever(similarity_top_k=1)
            test_results = retriever.retrieve("测试查询")
            print(f"✅ 索引验证成功，测试查询返回 {len(test_results)} 个结果")
        except Exception as test_error:
            print(f"⚠️ 索引验证失败: {str(test_error)}")
            print("这可能是导致查询错误的原因")

        print("--- 知识库加载成功！---")
        return index
    except Exception as e:
        print(f"❌ 加载知识库失败: {e}")
        import traceback
        traceback.print_exc()
        return None


# ============================== 数据处理 ==============================
# ===========数据处理json格式数据 ==========
def load_and_validate_json_files(data_dir: str) -> List[Dict]:
    """加载并验证JSON法律文件"""
    json_files = list(Path(data_dir).glob("*.json"))
    assert json_files, f"未找到JSON文件于 {data_dir}"

    all_data = []
    for json_file in json_files:
        with open(json_file, 'r', encoding='utf-8') as f:
            try:
                data = json.load(f)
                # 验证数据结构
                if not isinstance(data, list):
                    raise ValueError(f"文件 {json_file.name} 根元素应为列表")
                for item in data:
                    if not isinstance(item, dict):
                        raise ValueError(f"文件 {json_file.name} 包含非字典元素")
                    for k, v in item.items():
                        if not isinstance(v, str):
                            raise ValueError(f"文件 {json_file.name} 中键 '{k}' 的值不是字符串")
                all_data.extend({
                                    "content": item,
                                    "metadata": {"source_file": json_file.name, "content_type": "json_item"}
                                } for item in data)
            except Exception as e:
                raise RuntimeError(f"加载文件 {json_file} 失败: {str(e)}")

    print(f"成功加载 {len(all_data)} 个法律文件条目")
    return all_data

# ===========数据处理PDF格式数据 ==========
import sys
import os
from pdf2image import convert_from_path
import pytesseract
from pathlib import Path
from typing import List, Dict
import numpy as np
import pdfplumber

# 添加DeepDoc路径
deepdoc_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'ragflow-main')
if deepdoc_path not in sys.path:
    sys.path.append(deepdoc_path)

# 尝试导入DeepDoc
DEEPDOC_AVAILABLE = False
try:
    from deepdoc.vision import OCR
    DEEPDOC_AVAILABLE = True
    print("✅ 成功导入DeepDoc，将使用高级文档理解")
except ImportError as e:
    print(f"⚠️ 导入DeepDoc失败: {str(e)}")
    print("将回退到传统OCR方法")

# 设置 Tesseract 路径（Windows 专用）
pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

def load_pdfs_with_deepdoc(data_dir: str) -> List[Dict]:
    """使用DeepDoc处理PDF文件"""
    if not DEEPDOC_AVAILABLE:
        print("DeepDoc不可用，回退到传统OCR方法")
        return load_pdfs_traditional(data_dir)

    pdf_files = list(Path(data_dir).rglob("*.pdf"))
    all_data = []
    skipped_files = 0

    try:
        # 初始化DeepDoc OCR
        ocr = OCR()
        print(f"开始使用DeepDoc处理 {len(pdf_files)} 个PDF文件...")

        for pdf_file in pdf_files:
            try:
                print(f"处理文件: {pdf_file.name}")

                # 使用pdfplumber打开PDF
                with pdfplumber.open(pdf_file) as pdf:
                    text = ""
                    page_count = len(pdf.pages)

                    for i, page in enumerate(pdf.pages):
                        try:
                            # 转换为图像
                            img = page.to_image(resolution=300).annotated
                            img_array = np.array(img)

                            # DeepDoc OCR识别
                            ocr_results = ocr(img_array)
                            if ocr_results:
                                for box, (txt, score) in ocr_results:
                                    if score >= 0.5:  # 过滤低置信度结果
                                        text += txt + " "
                        except Exception as page_error:
                            print(f"处理第 {i+1} 页时出错: {str(page_error)}")
                            continue

                if not text.strip() or len(text.strip()) < 20:
                    print(f"警告：DeepDoc处理后PDF文件 {pdf_file.name} 仍无内容，已跳过。")
                    skipped_files += 1
                    continue

                all_data.append({
                    "content": text.strip(),
                    "metadata": {
                        "source_file": str(pdf_file.relative_to(data_dir)),
                        "content_type": "pdf_document_deepdoc"
                    }
                })
                print(f"✅ 成功处理 {pdf_file.name}，提取文本 {len(text)} 字符")

            except Exception as e:
                print(f"❌ DeepDoc处理PDF文件 {pdf_file.name} 时出错: {str(e)}")
                skipped_files += 1

    except Exception as e:
        print(f"❌ DeepDoc初始化失败: {str(e)}")
        print("回退到传统OCR方法")
        return load_pdfs_traditional(data_dir)

    print(f"DeepDoc处理完成：成功加载 {len(all_data)} 个 PDF 文件")
    if skipped_files > 0:
        print(f"有 {skipped_files} 个PDF文件被跳过")

    return all_data

def load_pdfs_traditional(data_dir: str) -> List[Dict]:
    """传统OCR方法处理PDF（回退方案）"""
    pdf_files = list(Path(data_dir).rglob("*.pdf"))
    all_data = []
    skipped_files = 0

    print(f"使用传统OCR方法处理 {len(pdf_files)} 个PDF文件...")

    for pdf_file in pdf_files:
        try:
            images = convert_from_path(pdf_file, dpi=300)  # 高分辨率提高识别率
            text = ""
            for img in images:
                text += pytesseract.image_to_string(img, lang='chi_sim')  # 简体中文

            if not text.strip() or len(text.strip()) < 20:
                print(f"警告：OCR后PDF文件 {pdf_file.name} 仍无内容，已跳过。")
                skipped_files += 1
                continue

            all_data.append({
                "content": text,
                "metadata": {
                    "source_file": str(pdf_file.relative_to(data_dir)),
                    "content_type": "pdf_document"
                }
            })
        except Exception as e:
            print(f"OCR处理PDF文件 {pdf_file.name} 时出错: {str(e)}")
            skipped_files += 1

    print(f"传统OCR处理完成：成功加载 {len(all_data)} 个 PDF 文件")
    if skipped_files > 0:
        print(f"有 {skipped_files} 个PDF文件被跳过")

    return all_data

# 主要的PDF加载函数，优先使用DeepDoc
def load_pdfs(data_dir: str) -> List[Dict]:
    """PDF加载主函数，优先使用DeepDoc，失败时回退到传统OCR"""
    return load_pdfs_with_deepdoc(data_dir)


# ===========数据处理excel格式数据 ==========
import pandas as pd
from pathlib import Path
import concurrent.futures
from functools import lru_cache




def create_deepdoc_table_overview(file_name: str, sheet_name: str, df: pd.DataFrame, columns: List[str]) -> str:
    """为DeepDoc创建优化的表格概览"""
    content = f"📊 表格文档概览\n"
    content += f"文件: {file_name} | 工作表: {sheet_name}\n\n"

    # 基本信息
    content += f"🔍 基本信息:\n"
    content += f"• 数据规模: {len(df)} 行 × {len(columns)} 列\n"
    content += f"• 数据字段: {', '.join(columns)}\n\n"

    # 字段分析
    content += f"📋 字段详情:\n"
    for i, col in enumerate(columns, 1):
        non_empty = df[col].astype(str).str.strip().ne('').sum()
        unique_count = df[col].nunique()
        content += f"{i}. {col}: {non_empty}/{len(df)} 有效值, {unique_count} 个不同值\n"

    # 数据预览
    content += f"\n📝 数据预览 (前5行):\n"
    for idx in range(min(5, len(df))):
        row_items = []
        for col in columns:
            value = str(df.iloc[idx][col]).strip()
            if value and value != 'nan':
                row_items.append(f"{col}={value}")
        if row_items:
            content += f"• 记录{idx+1}: {'; '.join(row_items)}\n"

    # 数据特征分析
    content += f"\n📈 数据特征:\n"
    for col in columns:
        col_data = df[col].astype(str).str.strip()
        non_empty_data = col_data[col_data.ne('') & col_data.ne('nan')]

        if len(non_empty_data) > 0:
            # 尝试数值分析
            try:
                numeric_data = pd.to_numeric(non_empty_data, errors='coerce').dropna()
                if len(numeric_data) > 0:
                    content += f"• {col}: 数值型 (范围: {numeric_data.min():.2f} - {numeric_data.max():.2f})\n"
                    continue
            except:
                pass

            # 文本分析
            if len(non_empty_data) > 0:
                sample_values = non_empty_data.head(3).tolist()
                content += f"• {col}: 文本型 (示例: {', '.join(sample_values)})\n"

    content += f"\n💡 此表格包含关于 {sheet_name} 的详细数据，可用于回答相关问题。\n"
    return content

def create_deepdoc_enhanced_chunk(file_name: str, sheet_name: str, chunk_df: pd.DataFrame,
                                 columns: List[str], chunk_idx: int, total_chunks: int,
                                 start_row: int, end_row: int) -> str:
    """创建增强的DeepDoc数据块"""
    content = f"📊 数据块: {file_name} - {sheet_name}\n"

    if total_chunks > 1:
        content += f"📍 位置: 第{chunk_idx + 1}/{total_chunks}块 (第{start_row + 1}-{end_row}行)\n"

    content += f"📋 字段: {', '.join(columns)}\n"

    # 🎯 新增：提取时间范围信息，提高时间查询的检索效果
    time_cols = [col for col in columns if any(keyword in str(col).lower() for keyword in ['时间', 'time', '日期', 'date'])]
    if time_cols and len(chunk_df) > 0:
        time_col = time_cols[0]
        try:
            first_time = str(chunk_df.iloc[0][time_col]).strip()
            last_time = str(chunk_df.iloc[-1][time_col]).strip()
            if first_time and first_time != 'nan' and last_time and last_time != 'nan':
                content += f"⏰ 时间范围: {first_time} 至 {last_time}\n"
        except:
            pass
    content += "\n"

    # 数据内容 - 优化格式
    content += f"📊 数据内容:\n"
    for idx, (_, row) in enumerate(chunk_df.iterrows()):
        row_items = []
        for col in columns:
            value = str(row[col]).strip()
            if value and value != 'nan':
                # 智能格式化
                try:
                    float_val = float(value)
                    if float_val.is_integer():
                        value = str(int(float_val))
                    else:
                        value = f"{float_val:.2f}"
                except:
                    pass
                row_items.append(f"{col}: {value}")

        if row_items:
            actual_row_num = start_row + idx + 1
            content += f"第{actual_row_num}行 → {' | '.join(row_items)}\n"

    # 添加块级统计
    if len(chunk_df) > 10:
        content += f"\n📈 本块统计:\n"
        content += f"• 数据行数: {len(chunk_df)}\n"

        # 分析数值列
        for col in columns:
            try:
                numeric_data = pd.to_numeric(chunk_df[col], errors='coerce').dropna()
                if len(numeric_data) > 0:
                    content += f"• {col}: 平均值 {numeric_data.mean():.2f}, 范围 {numeric_data.min():.2f}-{numeric_data.max():.2f}\n"
            except:
                pass

    return content

def load_excels_with_deepdoc(data_dir: str) -> List[Dict]:
    """
    优化的DeepDoc风格Excel处理，专门针对问答系统改进
    改进点：
    1. 增强语义表示和上下文信息
    2. 优化分片策略，提高检索效果
    3. 添加表格概览和关键信息摘要
    4. 改进数据格式，便于问答理解
    """
    excel_files = list(Path(data_dir).rglob("*.xlsx"))
    all_data = []
    skipped_files = 0

    print(f"🚀 开始使用优化的DeepDoc风格处理 {len(excel_files)} 个Excel文件...")

    for excel_file in excel_files:
        try:
            print(f"处理Excel文件: {excel_file.name}")

            # 使用pandas读取Excel文件
            try:
                sheets = pd.read_excel(excel_file, sheet_name=None, dtype=str, engine='openpyxl')

                for sheet_name, df in sheets.items():
                    if df.empty:
                        continue

                    # 清理列名
                    df.columns = df.columns.map(str)
                    columns = df.columns.tolist()
                    total_rows = len(df)

                    print(f"  📊 工作表 '{sheet_name}': {total_rows} 行 × {len(columns)} 列")

                    # 🎯 优化1: 首先创建表格概览节点（新增）
                    overview_content = create_deepdoc_table_overview(excel_file.name, sheet_name, df, columns)
                    all_data.append({
                        "content": overview_content,
                        "metadata": {
                            "source_file": str(excel_file.relative_to(Path(data_dir))),
                            "content_type": "excel_document_deepdoc",
                            "processing_method": "deepdoc_overview",
                            "sheet_name": sheet_name,
                            "node_type": "overview"
                        }
                    })

                    # 🎯 优化2: 改进的智能分片策略
                    if total_rows <= 500:
                        # 小表格：作为一个整体处理，但增强语义表示
                        chunk_size = total_rows
                        print(f"    📝 小表格，整体处理")
                    elif total_rows <= 2000:
                        # 中等表格：分成较小的语义块
                        chunk_size = 100  # 减小块大小，提高检索精度
                        print(f"    📚 中等表格，分片大小: {chunk_size}")
                    else:
                        # 大表格：分成更多小片，保持语义连贯性
                        chunk_size = 150  # 进一步减小，确保每个块都有足够的上下文
                        print(f"    📖 大表格，分片大小: {chunk_size}")

                    # 分片处理
                    num_chunks = (total_rows + chunk_size - 1) // chunk_size
                    print(f"    🔄 将分成 {num_chunks} 个片段处理")

                    for chunk_idx in range(num_chunks):
                        start_row = chunk_idx * chunk_size
                        end_row = min(start_row + chunk_size, total_rows)
                        chunk_df = df.iloc[start_row:end_row]

                        # 🎯 优化3: 使用增强的数据块生成
                        chunk_text = create_deepdoc_enhanced_chunk(
                            excel_file.name, sheet_name, chunk_df, columns,
                            chunk_idx, num_chunks, start_row, end_row
                        )

                        # 添加数据统计信息（仅对完整数据集进行统计）
                        if chunk_idx == 0:  # 只在第一个片段添加统计信息
                            chunk_text += f"\n数据统计 (基于完整数据集):\n"

                            # 分析数值列
                            numeric_cols = []
                            for col in columns:
                                try:
                                    numeric_data = pd.to_numeric(df[col], errors='coerce').dropna()
                                    if len(numeric_data) > 0:
                                        numeric_cols.append(col)
                                        # 计算基本统计信息
                                        mean_val = numeric_data.mean()
                                        min_val = numeric_data.min()
                                        max_val = numeric_data.max()
                                        chunk_text += f"- {col}: 数值列，有效数据 {len(numeric_data)} 个，"
                                        chunk_text += f"均值 {mean_val:.2f}，范围 [{min_val:.2f}, {max_val:.2f}]\n"
                                except:
                                    pass

                            # 分析文本列
                            text_cols = [col for col in columns if col not in numeric_cols]
                            if text_cols:
                                chunk_text += f"- 文本列: {', '.join(text_cols)}\n"

                        # 创建数据条目
                        all_data.append({
                            "content": chunk_text.strip(),
                            "metadata": {
                                "source_file": str(excel_file.relative_to(data_dir)),
                                "content_type": "excel_document_deepdoc",
                                "processing_method": "deepdoc_style_chunked",
                                "sheet_name": sheet_name,
                                "chunk_index": chunk_idx + 1,
                                "total_chunks": num_chunks,
                                "row_range": f"{start_row + 1}-{end_row}",
                                "content_size": len(chunk_text)
                            }
                        })

                        print(f"    ✅ 片段 {chunk_idx + 1}/{num_chunks} 处理完成，{len(chunk_text)} 字符")

            except Exception as e:
                print(f"pandas读取Excel失败: {str(e)}")
                skipped_files += 1
                continue

            print(f"✅ 成功处理 {excel_file.name}，创建了 {len([d for d in all_data if d['metadata']['source_file'] == str(excel_file.relative_to(data_dir))])} 个数据片段")

        except Exception as e:
            print(f"❌ 智能处理Excel文件 {excel_file.name} 时出错: {str(e)}")
            skipped_files += 1

    print(f"DeepDoc风格智能处理完成：成功加载 {len(all_data)} 个 Excel 数据片段")
    if skipped_files > 0:
        print(f"有 {skipped_files} 个Excel文件被跳过")

    return all_data

def load_excels_traditional(data_dir: str) -> List[Dict]:
    """
    传统Excel处理方法（回退方案）
    优化的Excel加载函数 v4。
    1. 使用并行处理加速多文件处理
    2. 使用LRU缓存减少重复计算
    3. 批量处理数据以提高效率
    4. 智能判断数据类型减少转换错误
    """
    excel_files = list(Path(data_dir).rglob("*.xlsx"))
    all_data = []

    print(f"使用传统方法处理 {len(excel_files)} 个Excel文件...")

    # 定义日期格式的缓存函数
    @lru_cache(maxsize=128)
    def format_datetime(value_str):
        try:
            dt = pd.to_datetime(value_str, errors='coerce')
            if pd.notna(dt):
                return dt.strftime('%Y-%m-%d %H:%M:%S')
        except Exception:
            pass
        return value_str

    # 智能处理单个值
    def process_value(col, value_str):
        # 如果值为空，直接返回"未知"
        if pd.isna(value_str) or value_str == "":
            return "未知"

        # 处理日期时间类型
        if isinstance(value_str, str) and ('时间' in str(col) or '日期' in str(col)):
            return format_datetime(value_str)

        # 处理数值类型
        if isinstance(value_str, str):
            try:
                value_float = float(value_str)
                # 只对非整数进行四舍五入
                if value_float.is_integer():
                    return str(int(value_float))
                else:
                    return str(round(value_float, 3))
            except (ValueError, TypeError):
                pass

        return str(value_str)

    # 处理单个Excel文件
    def process_excel_file(excel_file):
        file_data = []
        try:
            # 使用engine='openpyxl'提高与新版Excel的兼容性
            sheets = pd.read_excel(excel_file, sheet_name=None, dtype=str, engine='openpyxl')

            for sheet_name, df in sheets.items():
                if df.empty:
                    continue

                # 清理列名
                df.columns = df.columns.map(str)
                columns = df.columns.tolist()

                # 预处理整个数据框，而不是逐行处理
                # 优化1: 批量处理时间列
                time_cols = [col for col in columns if '时间' in str(col) or '日期' in str(col)]
                for col in time_cols:
                    if col in df.columns:
                        df[col] = df[col].apply(lambda x: format_datetime(x) if pd.notna(x) and isinstance(x, str) else x)

                # 处理每行数据
                for idx, row in df.iterrows():
                    # 构建键值对列表
                    parts = [f"'{col}': '{process_value(col, row[col])}'" for col in columns]

                    # 采用结构化文本格式
                    row_description = f"数据记录 - 文件: {excel_file.name}, 工作表: {sheet_name}, 行号: {idx + 2}. 内容: {{{', '.join(parts)}}}"

                    file_data.append({
                        "content": row_description,
                        "metadata": {
                            "source_file": str(excel_file.relative_to(data_dir)),
                            "content_type": "excel_row",
                            "sheet_name": sheet_name,
                            "row_number": idx + 2
                        }
                    })

        except Exception as e:
            print(f"加载或处理Excel文件 {excel_file} 时出错: {str(e)}")
        return file_data

    # 并行处理所有Excel文件
    with concurrent.futures.ThreadPoolExecutor(max_workers=min(8, len(excel_files))) as executor:
        results = list(executor.map(process_excel_file, excel_files))

    # 合并所有结果
    for result in results:
        all_data.extend(result)

    print(f"传统方法处理完成：成功加载 {len(all_data)} 个 Excel 文件")
    return all_data

# 主要的Excel加载函数，使用优化的DeepDoc方案
def load_excels(data_dir: str) -> List[Dict]:
    """Excel加载主函数，使用优化的DeepDoc方案，失败时回退到传统方法"""
    try:
        return load_excels_with_deepdoc(data_dir)
    except Exception as e:
        print(f"⚠️ 优化DeepDoc方案失败，回退到传统方法: {str(e)}")
        return load_excels_traditional(data_dir)

import os
def create_nodes_from_text(raw_data: List[Dict], node_parser) -> List[TextNode]:
    all_nodes = []
    for entry in raw_data:
        content = entry["content"]
        source_file = entry["metadata"].get("source_file", "unknown_source")

        doc = Document(text=content, metadata={"source_file": source_file, "content_type": "text_document"})
        nodes = node_parser.get_nodes_from_documents([doc])

        for i, node in enumerate(nodes):
            node.id_ = f"{source_file}::chunk_{i}"
            node.metadata.update({
                "source_file": source_file,
                "content_type": "text_document_chunk"
            })
            all_nodes.append(node)

    return all_nodes


# ===========专门处理PDF文本的节点生成函数 ==========

# 添加专门处理PDF文件的节点生成函数
def create_nodes_from_pdf(raw_data: List[Dict], node_parser) -> List[TextNode]:
    all_nodes = []
    warning_count = 0

    for entry in raw_data:
        content = entry["content"]
        if not content or len(content.strip()) < 10:
            source = entry["metadata"].get("source_file", "未知文件")
            print(f"警告：PDF文件 {source} 内容为空，已跳过")
            warning_count += 1
            continue

        source_file = entry["metadata"]["source_file"]

        doc = Document(text=content, metadata={
            "source_file": source_file,
            "content_type": "pdf_document"
        })

        try:
            nodes = node_parser.get_nodes_from_documents([doc])
            for i, node in enumerate(nodes):
                node.id_ = f"{source_file}::chunk_{i}"
                node.metadata.update({
                    "source_file": source_file,
                    "content_type": "pdf_document_chunk"
                })
                all_nodes.append(node)
        except Exception as e:
            print(f"处理PDF文件 {source_file} 时出错: {str(e)}")

    if warning_count > 0:
        print(f"注意: {warning_count} 个PDF文件因内容为空被跳过")

    print(f"成功从PDF内容生成 {len(all_nodes)} 个文本节点。")
    return all_nodes
def create_nodes_from_excel(raw_data: List[Dict], node_parser) -> List[TextNode]:
    """
    为Excel数据创建TextNode，支持两种格式：
    1. 传统格式：每行数据一个节点 (content_type: "excel_row")
    2. DeepDoc格式：智能分片的数据块 (content_type: "excel_document_deepdoc")
    """
    if not raw_data:
        print("警告: 没有Excel数据可以处理")
        return []

    all_nodes = []

    # 分离不同格式的数据
    traditional_entries = [entry for entry in raw_data if entry.get("metadata", {}).get("content_type") == "excel_row"]
    deepdoc_entries = [entry for entry in raw_data if entry.get("metadata", {}).get("content_type") == "excel_document_deepdoc"]

    print(f"处理Excel数据: 传统格式 {len(traditional_entries)} 条，DeepDoc格式 {len(deepdoc_entries)} 条")

    # 处理传统格式数据（按行处理）
    if traditional_entries:
        print("处理传统格式Excel数据...")
        # 按文件和工作表分组，以便更好地组织节点
        file_sheet_groups = {}
        for entry in traditional_entries:
            metadata = entry["metadata"]
            file_key = metadata.get("source_file", "unknown_file")
            sheet_key = metadata.get("sheet_name", "unknown_sheet")
            key = (file_key, sheet_key)

            if key not in file_sheet_groups:
                file_sheet_groups[key] = []
            file_sheet_groups[key].append(entry)

        # 处理每个文件-工作表组
        for (file_key, sheet_key), entries in file_sheet_groups.items():
            # 为每个组创建统一的前缀
            prefix = f"{file_key}::{sheet_key}"

            # 创建节点
            for entry in entries:
                metadata = entry["metadata"]
                row_number = metadata.get("row_number", "unknown_row")
                node_id = f"{prefix}::row_{row_number}"

                node = TextNode(
                    text=entry["content"],
                    id_=node_id,
                    metadata=metadata
                )
                all_nodes.append(node)

    # 处理DeepDoc格式数据
    if deepdoc_entries:
        print("处理DeepDoc格式Excel数据...")

        # 分离分片和非分片的DeepDoc数据
        chunked_entries = [entry for entry in deepdoc_entries if entry.get("metadata", {}).get("processing_method") == "deepdoc_style_chunked"]
        single_entries = [entry for entry in deepdoc_entries if entry.get("metadata", {}).get("processing_method") != "deepdoc_style_chunked"]

        print(f"  分片格式: {len(chunked_entries)} 条，单文档格式: {len(single_entries)} 条")

        # 处理分片格式数据（直接使用，无需进一步分块）
        if chunked_entries:
            print("  处理分片格式数据...")
            for entry in chunked_entries:
                metadata = entry["metadata"]
                file_key = metadata.get("source_file", "unknown_file")
                sheet_name = metadata.get("sheet_name", "unknown_sheet")
                chunk_index = metadata.get("chunk_index", 1)

                # 为分片创建唯一ID
                node_id = f"{file_key}::{sheet_name}::chunk_{chunk_index}"

                node = TextNode(
                    text=entry["content"],
                    id_=node_id,
                    metadata=metadata
                )
                all_nodes.append(node)

            print(f"    创建了 {len(chunked_entries)} 个分片节点")

        # 处理单文档格式数据（需要进一步分块）
        if single_entries:
            print("  处理单文档格式数据...")
            for entry in single_entries:
                metadata = entry["metadata"]
                file_key = metadata.get("source_file", "unknown_file")

                # 使用node_parser对长文本进行分块
                try:
                    # 创建临时文档对象（Document已在文件顶部导入）
                    doc = Document(text=entry["content"], metadata=metadata)

                    # 使用node_parser进行分块
                    nodes = node_parser.get_nodes_from_documents([doc])

                    # 为每个分块节点设置唯一ID和元数据
                    for i, node in enumerate(nodes):
                        node.id_ = f"{file_key}::deepdoc_auto_chunk_{i}"
                        node.metadata.update(metadata)
                        node.metadata["auto_chunk_index"] = i
                        all_nodes.append(node)

                except Exception as e:
                    print(f"使用node_parser处理DeepDoc Excel数据失败: {str(e)}")
                    # 回退到简单分块
                    node_id = f"{file_key}::deepdoc_full"
                    node = TextNode(
                        text=entry["content"],
                        id_=node_id,
                        metadata=metadata
                    )
                    all_nodes.append(node)



    print(f"从Excel数据中创建了 {len(all_nodes)} 个文本节点")
    return all_nodes


# ================== 界面组件 ==================
# 修改: 使用自定义的HTML/CSS渲染聊天气泡
def display_chat_message(message):
    """根据角色渲染用户或助手的聊天气泡"""
    role = message["role"]
    content = message.get("cleaned", message["content"])

    if role == "user":
        st.markdown(f'<div class="user-bubble">{content}</div>', unsafe_allow_html=True)
    elif role == "assistant":
        # 为每个消息创建唯一的键，避免UI元素ID冲突
        message_id = f"msg_{id(message)}"
        
        # 1. 显示回答文本
        st.markdown(f'<div class="assistant-bubble">{content}</div>', unsafe_allow_html=True)

        # 2. 显示参考文献
        if "reference_nodes" in message and message["reference_nodes"]:
            reference_key = f"ref_expanded_{message_id}"
            if reference_key not in st.session_state:
                st.session_state[reference_key] = False
                
            # 使用Streamlit按钮来控制参考文献的展开状态
            if st.button(
                "📚 查看参考文献" if not st.session_state[reference_key] else "📚 隐藏参考文献",
                key=f"ref_btn_{message_id}",
                type="secondary"
            ):
                st.session_state[reference_key] = not st.session_state[reference_key]
                st.rerun()  # 重新加载页面以更新展开状态
                
            # 基于会话状态显示参考文献
            if st.session_state[reference_key]:
                st.markdown("### 参考文献")
                for idx, node in enumerate(message["reference_nodes"], 1):
                    meta = node.node.metadata
                    source_file = meta.get("source_file", "未知文件")
                    
                    st.markdown(f"""
                    <div class="reference-expander">
                        <p><b>[{idx}] 来源:</b> {source_file}</p>
                        <p><b>相关度:</b> {node.score:.4f}</p>
                        <p><b>内容片段:</b></p>
                        <blockquote>{node.node.text}</blockquote>
                    </div>
                    """, unsafe_allow_html=True)

    # 添加一个清除浮动的div，防止布局错乱
    st.markdown('<div style="clear: both;"></div>', unsafe_allow_html=True)


def init_chat_interface():
    if "messages" not in st.session_state:
        st.session_state.messages = []

    for msg in st.session_state.messages:
        # 使用display_chat_message函数统一显示所有消息
        display_chat_message(msg)


# 这部分功能已移至display_chat_message函数内直接实现


# 新增：记录系统无法回答的问题
def log_unanswerable_question(question: str, context_nodes=None):
    """
    记录系统无法回答的问题，以便后续改进知识库
    
    Args:
        question: 用户提问的问题
        context_nodes: 检索到的上下文节点（如果有）
    """
    try:
        log_dir = Path("./logs")
        log_dir.mkdir(exist_ok=True)
        
        log_file = log_dir / "unanswerable_questions.jsonl"
        
        # 准备日志条目
        log_entry = {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "question": question,
            "retrieved_contexts": []
        }
        
        # 如果有上下文节点，记录它们的信息
        if context_nodes:
            for node in context_nodes:
                log_entry["retrieved_contexts"].append({
                    "text": node.node.text[:200] + "..." if len(node.node.text) > 200 else node.node.text,
                    "source": node.node.metadata.get("source_file", "未知来源"),
                    "score": node.score if hasattr(node, "score") else None
                })
        
        # 追加到日志文件
        with open(log_file, "a", encoding="utf-8") as f:
            f.write(json.dumps(log_entry, ensure_ascii=False) + "\n")
            
        print(f"已记录无法回答的问题: {question}")
    except Exception as e:
        print(f"记录无法回答的问题时出错: {str(e)}")


# ================== 主程序 ==================
def main():
    load_css("frontend/style.css")

    # 初始化按钮计数器
    if "button_counter" not in st.session_state:
        st.session_state.button_counter = 0

    # 确保messages已初始化
    if "messages" not in st.session_state:
        st.session_state.messages = []

    # --- 侧边栏 ---
    with st.sidebar:
        st.image("assets/Picture/lixiahe.png", width=80)
        st.title("控制与信息")
        st.info("欢迎使用南水北调水利问答助手。本系统旨在提供专业、准确的信息。")



        # 清除聊天记录按钮 - 添加唯一key
        if st.button("清除聊天记录", use_container_width=True, type="primary", key="clear_chat_button"):
            st.session_state.messages = []
            # 同时清除后端的对话内存
            if "chat_engine" in st.session_state:
                st.session_state.chat_engine.reset()
            st.rerun()

        # 重新构建索引按钮 - 应用新的检索参数
        if st.button("🔄 重新构建索引", use_container_width=True, type="secondary", key="rebuild_index_button"):
            with st.spinner("正在重新构建索引，应用新的检索参数..."):
                # 清除缓存的索引
                if "index" in st.session_state:
                    del st.session_state["index"]
                # 清除聊天引擎，强制重新初始化
                if "chat_engine" in st.session_state:
                    del st.session_state["chat_engine"]
                st.success("✅ 索引重建完成！新的检索参数已生效。")
                st.rerun()

        # 导出整个对话为文本文件 - 添加唯一key
        if st.session_state.messages and st.button("导出对话为文本", use_container_width=True,
                                                   key="export_all_text_button"):
            try:
                content = "南水北调水利问答记录\n\n"

                for msg in st.session_state.messages:
                    role = msg["role"]
                    text = msg.get("cleaned", msg["content"])

                    if role == "user":
                        content += f"问题:\n{text}\n\n"
                    elif role == "assistant":
                        content += f"回答:\n{text}\n\n"

                content += f"生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}"
                text_bytes = content.encode('utf-8')

                timestamp = time.strftime("%Y%m%d-%H%M%S")
                b64 = base64.b64encode(text_bytes).decode()
                href = f'<a href="data:text/plain;charset=utf-8;base64,{b64}" download="南水北调问答-{timestamp}.txt">点击下载文本文件</a>'
                st.markdown(href, unsafe_allow_html=True)
            except Exception as e:
                st.error(f"生成文本时出错: {str(e)}")

    # --- 主界面 ---
    st.title("南水北调水利问答助手 💧")
    st.markdown("请输入您的问题，我们将基于最新的研究成果和工程实践为您解答。")

    embed_model, llm, reranker = init_models()
    # 用下面这行代码替换掉原来复杂的if/else数据加载逻辑
    index = load_index()

    # 如果加载失败，显示错误并停止应用
    if index is None:
        st.error(
            "未能加载知识库。请先在终端运行 'python build_knowledge_base.py' 来构建知识库。"
        )
        st.stop()  # 停止执行


    # --- 对话引擎核心修改：暂时使用简单检索器避免API连接问题 ---
    if "chat_engine" not in st.session_state:
        with st.spinner("正在初始化对话引擎..."):
            # 暂时使用简单的向量检索器，避免QueryFusionRetriever的API调用问题
            retriever = index.as_retriever(similarity_top_k=Config.TOP_K)

            # 如果需要使用融合检索，可以在API连接稳定后启用以下代码：
            # vector_retriever = index.as_retriever(similarity_top_k=Config.TOP_K)
            # nodes = list(index.docstore.docs.values())
            # if nodes:
            #     bm25_retriever = BM25Retriever.from_defaults(
            #         nodes=nodes,
            #         similarity_top_k=Config.TOP_K
            #     )
            #     retriever = QueryFusionRetriever(
            #         retrievers=[vector_retriever, bm25_retriever],
            #         similarity_top_k=Config.TOP_K,
            #         num_queries=4,
            #         use_async=True,
            #         verbose=True
            #     )

            # 3. 定义简洁的提示词模板
            QA_PROMPT_TMPL_STR = (
                "你是南水北调水利问答专家。请基于提供的上下文资料准确回答问题。\n\n"

                "**上下文资料：**\n"
                "---------------------\n"
                "{context_str}\n"
                "---------------------\n\n"

                "**回答要求：**\n"
                "1. 仅基于上述资料回答，不使用外部知识\n"
                "2. 每个事实都要标注来源：[引用来源 n]\n"
                "3. 信息不足时明确说明：'根据提供的资料，我无法回答这个问题'\n"
                "4. 超出专业范围时说明：'这个问题超出了南水北调水利领域'\n\n"

                "**问题：** {query_str}\n\n"
                "请提供准确、专业的回答："
            )

            qa_prompt_tmpl = PromptTemplate(QA_PROMPT_TMPL_STR)

            # 添加相关度阈值过滤器，确保只有相关度足够高的文档才被用于回答问题
            class RelevanceScoreNodePostprocessor:
                """过滤掉相关度低于阈值的节点"""
                def __init__(self, threshold=0.3):
                    self.threshold = threshold
                    
                def postprocess_nodes(self, nodes, query_bundle):
                    # 过滤掉相关度低于阈值的节点
                    filtered_nodes = [node for node in nodes if node.score >= self.threshold]
                    
                    # 如果过滤后没有节点，返回一个空列表，这将触发"无法回答"逻辑
                    if not filtered_nodes and nodes:
                        print(f"警告：所有检索到的节点相关度都低于阈值 {self.threshold}，将返回空结果")
                    
                    return filtered_nodes

            # 创建内容长度限制器
            class ContentLengthLimiter:
                """限制检索内容的总长度，避免token超限"""
                def __init__(self, max_total_length=30000, max_node_length=15000):
                    self.max_total_length = max_total_length
                    self.max_node_length = max_node_length

                def postprocess_nodes(self, nodes, query_bundle):
                    # 限制单个节点长度和总长度
                    limited_nodes = []
                    total_length = 0

                    for node in nodes:
                        # 获取原始文本
                        original_text = node.node.text if hasattr(node, 'node') else node.text

                        # 截断过长的节点
                        if len(original_text) > self.max_node_length:
                            truncated_text = original_text[:self.max_node_length] + "...[内容已截断]"

                            # 创建新的文本节点
                            new_text_node = TextNode(
                                text=truncated_text,
                                id_=node.node.id_ if hasattr(node, 'node') else node.id_,
                                metadata=node.node.metadata if hasattr(node, 'node') else node.metadata
                            )

                            # 创建新的 NodeWithScore
                            if hasattr(node, 'score'):
                                new_node = NodeWithScore(node=new_text_node, score=node.score)
                            else:
                                new_node = new_text_node
                        else:
                            new_node = node
                            truncated_text = original_text

                        # 检查总长度
                        if total_length + len(truncated_text) <= self.max_total_length:
                            limited_nodes.append(new_node)
                            total_length += len(truncated_text)
                        else:
                            # 如果添加这个节点会超出限制，就停止
                            break

                    print(f"📊 内容长度控制: 保留 {len(limited_nodes)}/{len(nodes)} 个节点，总长度 {total_length:,} 字符")
                    return limited_nodes

            # 创建相关度过滤器，设置阈值
            relevance_filter = RelevanceScoreNodePostprocessor(
                threshold=Config.RELEVANCE_THRESHOLD
            )

            # 创建内容长度限制器
            content_limiter = ContentLengthLimiter(
                max_total_length=Config.MAX_CONTEXT_LENGTH,
                max_node_length=Config.MAX_NODE_LENGTH
            )

            # 4. 使用强大的融合检索器创建带记忆的聊天引擎
            st.session_state.chat_engine = ContextChatEngine.from_defaults(
                retriever=retriever,
                llm=llm,
                memory=ChatMemoryBuffer.from_defaults(token_limit=4096),
                node_postprocessors=[reranker, relevance_filter, content_limiter],  # 重排序 → 相关度过滤 → 长度限制
                context_prompt=qa_prompt_tmpl,
                verbose=True
            )

    # --- 聊天界面 ---
    # 显示历史消息
    for msg in st.session_state.messages:
        display_chat_message(msg)

    # 处理用户输入
    if prompt := st.chat_input("请输入您的问题..."):
        # 将用户消息添加到显示列表并立即显示
        user_message = {"role": "user", "content": prompt}
        st.session_state.messages.append(user_message)
        display_chat_message(user_message)

        # 处理查询并显示助手响应
        with st.spinner("正在使用融合检索模式检索并生成回答..."):
            # 直接使用我们强大的融合检索聊天引擎进行对话
            response = st.session_state.chat_engine.chat(prompt)
            response_text = str(response)

            # 清理回复文本（移除可能的思考过程标签）
            cleaned_response = re.sub(r'<think>.*?</think>', '', response_text, flags=re.DOTALL).strip()

            print(f"\n\n===== 调试信息 =====")
            print(f"模型回复原始文本长度: {len(response_text)}")
            print(f"清理后回复长度: {len(cleaned_response)}")
            
            # 从响应对象中获取源节点
            source_nodes = response.source_nodes
            
            # 检查是否无法回答问题
            if "我无法回答这个问题" in cleaned_response or "超出了我的专业范围" in cleaned_response:
                # 记录无法回答的问题
                log_unanswerable_question(prompt, source_nodes)

            # 创建助手消息对象
            assistant_message = {
                "role": "assistant",
                "content": response_text,
                "cleaned": cleaned_response,
                "reference_nodes": source_nodes
            }

            # 记录调试信息
            print(f"助手消息已创建")
            print(f"消息键: {list(assistant_message.keys())}")

            st.session_state.messages.append(assistant_message)
            print(f"消息已添加到会话状态，当前消息总数: {len(st.session_state.messages)}")

            # 保存当前问答对，用于文本导出
            st.session_state.last_qa = {
                "question": prompt,
                "answer": cleaned_response
            }

            # 使用rerun来刷新界面并显示助手的完整消息
            st.rerun()

    # 显示下载按钮（对于最近的一次对话）
    if "last_qa" in st.session_state:
        col1, col2 = st.columns([3, 1])
        with col2:
            # 直接准备文本内容，不需要额外的按钮点击
            q = st.session_state.last_qa["question"]
            a = st.session_state.last_qa["answer"]
            content = generate_single_qa_text(q, a).decode('utf-8')
            timestamp = time.strftime("%Y%m%d-%H%M%S")

            # 使用与侧边栏相同的下载按钮实现
            st.download_button(
                label="📄 导出此问答",
                data=content,
                file_name=f"南水北调问答-{timestamp}.txt",
                mime="text/plain",
                key=f"download_single_{len(st.session_state.messages)}_{timestamp}"
            )

if __name__ == "__main__":
    main()