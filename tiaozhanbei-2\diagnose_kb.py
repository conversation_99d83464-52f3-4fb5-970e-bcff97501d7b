#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
知识库诊断脚本
分析多文档知识库的检索效果问题
"""

import chromadb
import json
from pathlib import Path
from llama_index.core import VectorStoreIndex, StorageContext
from llama_index.vector_stores.chroma import ChromaVectorStore
from llama_index.embeddings.huggingface import Hugging<PERSON>aceEmbedding
from llama_index.core import Settings

class Config:
    EMBED_MODEL_PATH = r"D:\pythonProject\embedding_model\BAAI\bge-large-zh-v1___5"
    VECTOR_DB_DIR = "./chroma_db"
    COLLECTION_NAME = "chinese_labor_laws"

def init_embedding_model():
    """初始化嵌入模型"""
    embed_model = HuggingFaceEmbedding(
        model_name=Config.EMBED_MODEL_PATH,
        trust_remote_code=True
    )
    Settings.embed_model = embed_model
    return embed_model

def analyze_knowledge_base():
    """分析知识库状态"""
    print("🔍 开始分析知识库状态...")
    
    # 检查向量数据库是否存在
    if not Path(Config.VECTOR_DB_DIR).exists():
        print("❌ 向量数据库不存在，请先构建知识库")
        return
    
    # 初始化嵌入模型
    embed_model = init_embedding_model()
    
    # 连接到现有的向量数据库
    chroma_client = chromadb.PersistentClient(path=Config.VECTOR_DB_DIR)
    chroma_collection = chroma_client.get_or_create_collection(
        name=Config.COLLECTION_NAME,
        metadata={"hnsw:space": "cosine"}
    )
    
    storage_context = StorageContext.from_defaults(
        vector_store=ChromaVectorStore(chroma_collection=chroma_collection)
    )
    
    # 加载索引
    index = VectorStoreIndex.from_vector_store(
        storage_context.vector_store,
        storage_context=storage_context
    )
    
    # 分析文档数量
    doc_count = len(storage_context.docstore.docs)
    print(f"📊 知识库统计:")
    print(f"  - 总文档数: {doc_count}")
    
    if doc_count == 0:
        print("❌ 知识库为空，请检查数据导入")
        return
    
    # 分析文档内容
    docs = list(storage_context.docstore.docs.values())
    doc_sizes = [len(doc.text) for doc in docs]
    
    print(f"  - 文档大小统计:")
    print(f"    * 平均大小: {sum(doc_sizes)/len(doc_sizes):.0f} 字符")
    print(f"    * 最小大小: {min(doc_sizes)} 字符")
    print(f"    * 最大大小: {max(doc_sizes):,} 字符")
    
    # 分析文档来源
    sources = {}
    for doc in docs:
        source = doc.metadata.get('source', 'unknown')
        if source in sources:
            sources[source] += 1
        else:
            sources[source] = 1
    
    print(f"  - 文档来源分布:")
    for source, count in sources.items():
        print(f"    * {source}: {count} 个文档")
    
    return index, docs

def test_retrieval_with_different_params(index, test_query="南水北调工程的主要作用是什么？"):
    """测试不同参数下的检索效果"""
    print(f"\n🧪 测试检索效果 (查询: {test_query})")
    
    # 测试不同的TOP_K值
    top_k_values = [1, 3, 5, 10]
    
    for top_k in top_k_values:
        print(f"\n--- TOP_K = {top_k} ---")
        retriever = index.as_retriever(similarity_top_k=top_k)
        nodes = retriever.retrieve(test_query)
        
        print(f"检索到 {len(nodes)} 个节点:")
        for i, node in enumerate(nodes):
            score = getattr(node, 'score', 'N/A')
            source = node.metadata.get('source', 'unknown')
            text_preview = node.text[:100] + "..." if len(node.text) > 100 else node.text
            print(f"  {i+1}. 相似度: {score:.4f} | 来源: {source}")
            print(f"     内容预览: {text_preview}")

def test_relevance_thresholds(index, test_query="南水北调工程的主要作用是什么？"):
    """测试不同相关度阈值的影响"""
    print(f"\n🎯 测试相关度阈值影响")
    
    from llama_index.core.postprocessor import SimilarityPostprocessor
    
    thresholds = [0.1, 0.2, 0.3, 0.4, 0.5]
    
    for threshold in thresholds:
        print(f"\n--- 相关度阈值 = {threshold} ---")
        retriever = index.as_retriever(similarity_top_k=10)
        nodes = retriever.retrieve(test_query)
        
        # 应用相关度过滤
        similarity_filter = SimilarityPostprocessor(similarity_cutoff=threshold)
        filtered_nodes = similarity_filter.postprocess_nodes(nodes)
        
        print(f"原始检索: {len(nodes)} 个节点")
        print(f"过滤后: {len(filtered_nodes)} 个节点")
        
        if filtered_nodes:
            avg_score = sum(getattr(node, 'score', 0) for node in filtered_nodes) / len(filtered_nodes)
            print(f"平均相似度: {avg_score:.4f}")
        else:
            print("⚠️ 所有节点都被过滤掉了！")

def main():
    """主函数"""
    try:
        index, docs = analyze_knowledge_base()
        if index:
            test_retrieval_with_different_params(index)
            test_relevance_thresholds(index)
            
            print(f"\n💡 建议:")
            print(f"  1. 如果文档数量较多，建议增加 TOP_K 到 5-10")
            print(f"  2. 如果检索结果被过度过滤，建议降低 RELEVANCE_THRESHOLD 到 0.1-0.2")
            print(f"  3. 如果需要更多上下文，建议增加 RERANK_TOP_K 到 3-5")
            
    except Exception as e:
        print(f"❌ 诊断过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
