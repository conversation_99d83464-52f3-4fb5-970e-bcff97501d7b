#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化的Excel处理方案
"""

import sys
from pathlib import Path
from app import load_excels_optimized_for_qa, create_nodes_from_excel
from llama_index.core.node_parser import SentenceSplitter

def test_optimized_excel_processing():
    """测试优化的Excel处理方案"""
    print("🧪 测试优化的Excel处理方案")
    
    # 设置数据目录
    data_dir = "./data"
    
    if not Path(data_dir).exists():
        print(f"❌ 数据目录不存在: {data_dir}")
        return False
    
    # 检查是否有Excel文件
    excel_files = list(Path(data_dir).rglob("*.xlsx"))
    if not excel_files:
        print(f"❌ 在 {data_dir} 中没有找到Excel文件")
        return False
    
    print(f"📊 找到 {len(excel_files)} 个Excel文件:")
    for file in excel_files:
        print(f"  - {file.name}")
    
    try:
        # 测试优化的Excel数据加载
        print("\n🔄 测试优化的Excel数据加载...")
        excel_data = load_excels_optimized_for_qa(data_dir)
        
        print(f"✅ 成功加载 {len(excel_data)} 个数据条目")
        
        # 分析数据类型分布
        type_counts = {}
        for entry in excel_data:
            content_type = entry.get("metadata", {}).get("content_type", "unknown")
            node_type = entry.get("metadata", {}).get("node_type", "unknown")
            key = f"{content_type}_{node_type}"
            type_counts[key] = type_counts.get(key, 0) + 1
        
        print(f"\n📈 数据类型分布:")
        for data_type, count in type_counts.items():
            print(f"  - {data_type}: {count} 条")
        
        # 分析内容长度
        content_lengths = [len(entry["content"]) for entry in excel_data]
        if content_lengths:
            avg_length = sum(content_lengths) / len(content_lengths)
            min_length = min(content_lengths)
            max_length = max(content_lengths)
            
            print(f"\n📏 内容长度统计:")
            print(f"  - 平均长度: {avg_length:.0f} 字符")
            print(f"  - 最小长度: {min_length} 字符")
            print(f"  - 最大长度: {max_length:,} 字符")
        
        # 显示样本内容
        print(f"\n📝 样本内容:")
        for i, entry in enumerate(excel_data[:3]):
            content_type = entry.get("metadata", {}).get("content_type", "unknown")
            node_type = entry.get("metadata", {}).get("node_type", "unknown")
            content_preview = entry["content"][:200] + "..." if len(entry["content"]) > 200 else entry["content"]
            print(f"  样本 {i+1} ({content_type}_{node_type}):")
            print(f"    {content_preview}")
            print()
        
        # 测试节点创建
        print("🔄 测试节点创建...")
        node_parser = SentenceSplitter(chunk_size=1024, chunk_overlap=200)
        nodes = create_nodes_from_excel(excel_data, node_parser)
        
        print(f"✅ 成功创建 {len(nodes)} 个节点")
        
        # 分析节点
        node_lengths = [len(node.text) for node in nodes]
        if node_lengths:
            avg_node_length = sum(node_lengths) / len(node_lengths)
            min_node_length = min(node_lengths)
            max_node_length = max(node_lengths)
            
            print(f"\n📊 节点统计:")
            print(f"  - 平均节点长度: {avg_node_length:.0f} 字符")
            print(f"  - 最小节点长度: {min_node_length} 字符")
            print(f"  - 最大节点长度: {max_node_length:,} 字符")
        
        # 分析节点类型
        node_types = {}
        for node in nodes:
            node_type = node.metadata.get("node_type", "unknown")
            node_types[node_type] = node_types.get(node_type, 0) + 1
        
        print(f"\n🏷️ 节点类型分布:")
        for node_type, count in node_types.items():
            print(f"  - {node_type}: {count} 个节点")
        
        # 显示节点样本
        print(f"\n📄 节点样本:")
        for i, node in enumerate(nodes[:3]):
            node_type = node.metadata.get("node_type", "unknown")
            text_preview = node.text[:150] + "..." if len(node.text) > 150 else node.text
            print(f"  节点 {i+1} ({node_type}):")
            print(f"    ID: {node.id_}")
            print(f"    内容: {text_preview}")
            print()
        
        print("✅ 优化的Excel处理方案测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def compare_with_traditional():
    """比较优化方案与传统方案"""
    print("\n🔄 比较优化方案与传统方案...")
    
    from app import load_excels_traditional
    
    data_dir = "./data"
    
    try:
        # 加载传统方案数据
        print("加载传统方案数据...")
        traditional_data = load_excels_traditional(data_dir)
        
        # 加载优化方案数据
        print("加载优化方案数据...")
        optimized_data = load_excels_optimized_for_qa(data_dir)
        
        print(f"\n📊 数据量对比:")
        print(f"  - 传统方案: {len(traditional_data)} 条数据")
        print(f"  - 优化方案: {len(optimized_data)} 条数据")
        
        # 内容长度对比
        traditional_lengths = [len(entry["content"]) for entry in traditional_data]
        optimized_lengths = [len(entry["content"]) for entry in optimized_data]
        
        if traditional_lengths and optimized_lengths:
            print(f"\n📏 平均内容长度对比:")
            print(f"  - 传统方案: {sum(traditional_lengths)/len(traditional_lengths):.0f} 字符")
            print(f"  - 优化方案: {sum(optimized_lengths)/len(optimized_lengths):.0f} 字符")
        
        print("✅ 方案对比完成")
        
    except Exception as e:
        print(f"❌ 对比失败: {str(e)}")

def main():
    """主函数"""
    print("🚀 开始测试优化的Excel处理方案\n")
    
    # 测试优化方案
    success = test_optimized_excel_processing()
    
    if success:
        # 比较方案
        compare_with_traditional()
    
    print("\n🏁 测试完成")

if __name__ == "__main__":
    main()
