#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试token优化效果
"""

import sys
from pathlib import Path

def estimate_tokens(text):
    """估算文本的token数量（粗略估计）"""
    # 中文字符大约1.5个token，英文单词大约1个token
    chinese_chars = sum(1 for char in text if '\u4e00' <= char <= '\u9fff')
    other_chars = len(text) - chinese_chars
    return int(chinese_chars * 1.5 + other_chars * 0.25)

def test_excel_processing():
    """测试Excel处理的token优化"""
    print("🧪 测试Excel处理的token优化效果...")
    
    # 添加当前目录到路径
    current_dir = Path(__file__).parent
    if str(current_dir) not in sys.path:
        sys.path.append(str(current_dir))
    
    try:
        from app import load_excels_with_deepdoc
        
        # 测试处理Excel文件
        results = load_excels_with_deepdoc("data")
        
        print(f"\n📊 处理结果统计:")
        print(f"- 处理的Excel文件数量: {len(results)}")
        
        total_tokens = 0
        for i, result in enumerate(results):
            content = result['content']
            tokens = estimate_tokens(content)
            total_tokens += tokens
            
            print(f"\n文件 {i+1}: {result['metadata']['source_file']}")
            print(f"  - 内容长度: {len(content)} 字符")
            print(f"  - 估算token数: {tokens}")
            print(f"  - 内容预览: {content[:100]}...")
        
        print(f"\n🎯 总体统计:")
        print(f"- 总token数估算: {total_tokens}")
        print(f"- 平均每文件token数: {total_tokens // len(results) if results else 0}")
        
        # 检查是否在安全范围内
        max_safe_tokens = 50000  # 安全的token上限
        if total_tokens < max_safe_tokens:
            print(f"✅ Token数量在安全范围内 ({total_tokens} < {max_safe_tokens})")
        else:
            print(f"⚠️ Token数量可能过多 ({total_tokens} >= {max_safe_tokens})")
            print("建议进一步优化处理策略")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")

def test_chunking_config():
    """测试分块配置"""
    print("\n🔧 当前分块配置:")
    
    try:
        from app import Config
        
        print(f"- 分块模式: {Config.CHUNKING_MODE}")
        print(f"- 块大小: {Config.CHUNK_SIZE}")
        print(f"- 重叠大小: {Config.CHUNK_OVERLAP}")
        print(f"- 检索TOP_K: {Config.TOP_K}")
        print(f"- 重排序TOP_K: {Config.RERANK_TOP_K}")
        print(f"- 相关度阈值: {Config.RELEVANCE_THRESHOLD}")
        
    except Exception as e:
        print(f"❌ 获取配置失败: {str(e)}")

def main():
    """主函数"""
    print("🔍 Token优化效果测试")
    print("=" * 50)
    
    test_chunking_config()
    test_excel_processing()
    
    print("\n" + "=" * 50)
    print("💡 优化建议:")
    print("1. 如果token数量仍然过多，可以进一步减少TOP_K和RERANK_TOP_K")
    print("2. 可以增加Excel行数限制（当前500行）")
    print("3. 可以减少CHUNK_SIZE（当前1024）")
    print("4. 可以提高SEMANTIC_BREAKPOINT_THRESHOLD（当前95）")

if __name__ == "__main__":
    main()
