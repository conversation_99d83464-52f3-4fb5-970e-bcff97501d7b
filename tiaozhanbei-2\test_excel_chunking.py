#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Excel分片处理功能
验证新的智能分片策略是否能够完整处理大型Excel文件
"""

import sys
import os
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

# 导入必要的模块
from app import load_excels_with_deepdoc, create_nodes_from_excel, init_models

def test_excel_chunking():
    """测试Excel分片处理功能"""
    print("🧪 开始测试Excel分片处理功能...")
    
    # 1. 测试数据加载
    print("\n📂 步骤1: 测试Excel数据加载...")
    data_dir = "./data"
    
    if not os.path.exists(data_dir):
        print(f"❌ 数据目录 {data_dir} 不存在")
        return False
    
    # 加载Excel数据
    excel_data = load_excels_with_deepdoc(data_dir)
    
    if not excel_data:
        print("❌ 没有加载到Excel数据")
        return False
    
    print(f"✅ 成功加载 {len(excel_data)} 个Excel数据片段")
    
    # 2. 分析数据结构
    print("\n📊 步骤2: 分析数据结构...")
    
    # 统计不同类型的数据
    chunked_count = 0
    single_count = 0
    total_content_size = 0
    
    file_stats = {}
    
    for entry in excel_data:
        metadata = entry.get("metadata", {})
        source_file = metadata.get("source_file", "unknown")
        processing_method = metadata.get("processing_method", "unknown")
        content_size = len(entry.get("content", ""))
        
        total_content_size += content_size
        
        if processing_method == "deepdoc_style_chunked":
            chunked_count += 1
        else:
            single_count += 1
        
        # 统计每个文件的片段数
        if source_file not in file_stats:
            file_stats[source_file] = {
                "chunks": 0,
                "total_size": 0,
                "sheets": set()
            }
        
        file_stats[source_file]["chunks"] += 1
        file_stats[source_file]["total_size"] += content_size
        
        sheet_name = metadata.get("sheet_name")
        if sheet_name:
            file_stats[source_file]["sheets"].add(sheet_name)
    
    print(f"  分片格式数据: {chunked_count} 个")
    print(f"  单文档格式数据: {single_count} 个")
    print(f"  总内容大小: {total_content_size:,} 字符")
    
    # 3. 显示文件统计
    print("\n📋 步骤3: 文件处理统计...")
    for file_name, stats in file_stats.items():
        print(f"  📄 {file_name}:")
        print(f"    - 数据片段: {stats['chunks']} 个")
        print(f"    - 工作表数: {len(stats['sheets'])} 个")
        print(f"    - 总大小: {stats['total_size']:,} 字符")
        if stats['sheets']:
            print(f"    - 工作表: {', '.join(sorted(stats['sheets']))}")
    
    # 4. 测试节点创建
    print("\n🔧 步骤4: 测试节点创建...")
    
    try:
        # 初始化模型（获取node_parser）
        embed_model, llm, reranker = init_models()
        
        # 这里需要获取node_parser，但init_models没有返回它
        # 我们需要从app.py中导入相关配置
        from llama_index.core.node_parser import SemanticSplitterNodeParser
        from llama_index.embeddings.huggingface import HuggingFaceEmbedding
        
        # 创建语义分块器
        embed_model_for_chunking = HuggingFaceEmbedding(
            model_name="BAAI/bge-large-zh-v1.5",
            device='cuda' if os.environ.get('CUDA_AVAILABLE') == 'true' else 'cpu'
        )
        
        node_parser = SemanticSplitterNodeParser(
            buffer_size=1,
            breakpoint_percentile_threshold=95,
            embed_model=embed_model_for_chunking,
        )
        
        # 创建节点
        nodes = create_nodes_from_excel(excel_data, node_parser)
        
        print(f"✅ 成功创建 {len(nodes)} 个文本节点")
        
        # 分析节点
        node_sizes = [len(node.text) for node in nodes]
        avg_size = sum(node_sizes) / len(node_sizes) if node_sizes else 0
        min_size = min(node_sizes) if node_sizes else 0
        max_size = max(node_sizes) if node_sizes else 0
        
        print(f"  节点大小统计:")
        print(f"    - 平均大小: {avg_size:.0f} 字符")
        print(f"    - 最小大小: {min_size} 字符")
        print(f"    - 最大大小: {max_size:,} 字符")
        
        # 检查是否有超大节点
        large_nodes = [node for node in nodes if len(node.text) > 30000]
        if large_nodes:
            print(f"  ⚠️ 发现 {len(large_nodes)} 个超大节点 (>30K字符)")
            for node in large_nodes[:3]:  # 只显示前3个
                print(f"    - {node.id_}: {len(node.text):,} 字符")
        else:
            print(f"  ✅ 所有节点大小都在合理范围内")
        
    except Exception as e:
        print(f"❌ 节点创建失败: {str(e)}")
        return False
    
    # 5. 测试内容完整性
    print("\n🔍 步骤5: 测试内容完整性...")
    
    # 检查是否有数据丢失
    original_files = set()
    processed_files = set()
    
    # 从文件系统获取原始文件列表
    data_path = Path(data_dir)
    for excel_file in data_path.rglob("*.xlsx"):
        original_files.add(str(excel_file.relative_to(data_path)))
    
    # 从处理结果获取文件列表
    for entry in excel_data:
        source_file = entry.get("metadata", {}).get("source_file")
        if source_file:
            processed_files.add(source_file)
    
    missing_files = original_files - processed_files
    if missing_files:
        print(f"  ⚠️ 有 {len(missing_files)} 个文件未被处理:")
        for file in missing_files:
            print(f"    - {file}")
    else:
        print(f"  ✅ 所有 {len(original_files)} 个Excel文件都已处理")
    
    print("\n🎉 测试完成！")
    return True

def main():
    """主函数"""
    print("=" * 60)
    print("Excel分片处理功能测试")
    print("=" * 60)
    
    success = test_excel_chunking()
    
    if success:
        print("\n✅ 所有测试通过！新的分片处理功能工作正常。")
        print("\n💡 主要改进:")
        print("  - 支持大型Excel文件完整处理")
        print("  - 智能分片策略，避免数据丢失")
        print("  - 保持数据结构和统计信息")
        print("  - 优化的节点创建和索引")
    else:
        print("\n❌ 测试失败，请检查错误信息。")
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    main()
