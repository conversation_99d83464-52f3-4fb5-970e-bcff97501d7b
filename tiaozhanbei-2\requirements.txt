# 基础依赖
streamlit>=1.31.0
numpy>=1.24.0
pandas>=2.0.0
tqdm>=4.65.0
chromadb>=0.4.18
llama-index-core>=0.10.0
llama-index-embeddings-huggingface>=0.1.0
llama-index-llms-huggingface>=0.1.0
llama-index-llms-openai-like>=0.1.0
llama-index-vector-stores-chroma>=0.1.0
llama-index-retrievers-bm25>=0.1.0
# llama-index-postprocessor-sentence-transformer-rerank>=0.1.0  # 暂时注释，包名可能不正确
torch>=2.0.0
transformers>=4.30.0
sentence-transformers>=2.2.2
nest-asyncio>=1.5.6

# PDF处理
pypdf>=3.15.1
pdfplumber>=0.10.0
pdf2image>=1.16.3
pytesseract>=0.3.10  # 作为备用OCR

# Excel处理
openpyxl>=3.1.0  # Excel文件读写
xlrd>=2.0.1  # 旧版Excel文件支持

# DeepDoc依赖
onnxruntime>=1.15.0  # 用于模型推理
opencv-python>=4.8.0  # 用于图像处理
huggingface-hub>=0.19.0  # 用于下载模型
trio>=0.22.0  # 用于并发处理

# 其他工具
base58>=2.1.1  # 用于ID生成