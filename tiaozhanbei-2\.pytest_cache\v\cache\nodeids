["diagnose_kb.py::test_relevance_thresholds", "diagnose_kb.py::test_retrieval_with_different_params", "test_excel_chunking.py::test_excel_chunking", "test_excel_deepdoc.py::test_excel_processing", "test_gpu_config.py::test_app_gpu_config", "test_gpu_config.py::test_embedding_model_gpu", "test_gpu_config.py::test_gpu_availability", "test_gpu_config.py::test_pytorch_cuda", "test_optimized_deepdoc.py::test_optimized_deepdoc", "test_optimized_excel.py::test_optimized_excel_processing", "test_token_control.py::test_content_limiter", "test_token_control.py::test_token_control", "test_token_optimization.py::test_chunking_config", "test_token_optimization.py::test_excel_processing"]